package com.polarion.synchronizer.proxy.feishu;

import com.lark.project.Client;
import com.lark.project.core.Config;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.core.token.AccessTokenType;
import com.lark.project.core.token.GlobalTokenManager;
import com.lark.project.service.project.builder.ListProjectReq;
import com.lark.project.service.project.builder.ListProjectResp;
import com.lark.project.service.project.builder.ListProjectWorkItemTypeReq;
import com.lark.project.service.project.builder.ListProjectWorkItemTypeResp;
import com.polarion.core.util.logging.Logger;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 飞书项目SDK辅助类
 * 封装常用的SDK操作和客户端调用
 */
public class FeishuSDKHelper {

    private static final Logger logger = Logger.getLogger(FeishuSDKHelper.class);

    private final FeishuConnection connection;
    private Client client;
    private RequestOptions defaultOptions;

    public FeishuSDKHelper(@NotNull FeishuConnection connection) {
        this.connection = connection;
        this.client = createClient();
        this.defaultOptions = createRequestOptions();
    }
    
    /**
     * 创建SDK客户端
     */
    private Client createClient() {
        String baseUrl = getBaseUrl();
        String authMode = connection.getAuthMode();

        switch (authMode) {
            case "plugin_access_token":
                // 插件身份凭证模式 - 使用App ID和Secret，SDK自动管理token
                String userKey = connection.getUserKey();
                if (userKey != null && !userKey.isEmpty()) {
                    // 有 userKey 时使用标准插件访问凭证
                    return Client.newBuilder(connection.getPluginId(), connection.getPluginSecret())
                        .openBaseUrl(baseUrl)
                        .requestTimeout(30000)
                        .logReqAtDebug(true)
                        .build();
                } else {
                    // 无 userKey 时使用虚拟插件访问凭证类型
                    return Client.newBuilder(connection.getPluginId(), connection.getPluginSecret())
                        .accessTokenType(AccessTokenType.AccessTokenTypeVirtualPlugin)
                        .openBaseUrl(baseUrl)
                        .requestTimeout(30000)
                        .logReqAtDebug(true)
                        .build();
                }

            case "virtual_plugin_token":
            case "user_access_token":
                // 禁用token管理，需要手动传递token
                return Client.newBuilder("", "")
                    .openBaseUrl(baseUrl)
                    .requestTimeout(30000)
                    .disableTokenCache()
                    .logReqAtDebug(true)
                    .build();

            default:
                // 默认使用插件模式
                return Client.newBuilder("", "")
                    .openBaseUrl(baseUrl)
                    .requestTimeout(30000)
                    .disableTokenCache()
                    .logReqAtDebug(true)
                    .build();
        }
    }

    /**
     * 根据访问凭证类型创建请求选项
     */
    private RequestOptions createRequestOptions() {
        String authMode = connection.getAuthMode();

        switch (authMode) {
            case "plugin_access_token":
                // 插件模式由SDK自动管理token，只需要设置X-User-Key
                RequestOptions.Builder builder = RequestOptions.newBuilder();
                String userKey = connection.getUserKey();
                if (userKey != null && !userKey.isEmpty()) {
                	builder.userKey(userKey);
                }
                return builder.build();

            case "virtual_plugin_token":
            case "user_access_token":
                // 手动传递token
                String accessToken = connection.getAccessToken();
                if (accessToken != null && !accessToken.isEmpty()) {
                    return RequestOptions.newBuilder()
                        .accessToken(accessToken)
                        .build();
                }
                break;
        }

        return RequestOptions.newBuilder().build();
    }



    /**
     * 获取基础URL
     */
    private String getBaseUrl() {
        String baseUrl = connection.getServerUrl();
        return (baseUrl != null && !baseUrl.isEmpty()) ? baseUrl : "https://project.feishu.cn";
    }
    
    /**
     * 刷新客户端和请求选项
     * 当连接参数发生变化时调用此方法重新创建客户端
     */
    public void refreshClient() {
        this.client = createClient();
        this.defaultOptions = createRequestOptions();
        logger.debug("已刷新飞书SDK客户端和请求选项");
    }

    /**
     * 测试连接 - 仅验证token有效性
     * 通过SDK的token管理机制验证凭证是否有效，不进行实际的API调用
     */
    @Nullable
    public String testConnection() {
        try {
            logger.info("测试飞书连接，认证模式: " + connection.getAuthMode());

            String authMode = connection.getAuthMode();

            // 根据不同的认证模式进行token验证
            switch (authMode) {
                case "plugin_access_token":
                    return testPluginAccessToken();

                case "virtual_plugin_token":
                case "user_access_token":
                    return testManualAccessToken();

                default:
                    return "不支持的认证模式: " + authMode;
            }

        } catch (Exception e) {
            String errorMsg = "飞书连接测试出现异常: " + e.getMessage();
            logger.error(errorMsg, e);
            return errorMsg;
        }
    }

    /**
     * 测试插件访问凭证模式的token有效性
     */
    private String testPluginAccessToken() {
        try {
            // 验证插件ID和密钥是否配置
            String pluginId = connection.getPluginId();
            String pluginSecret = connection.getPluginSecret();

            if (pluginId == null || pluginId.trim().isEmpty()) {
                return "插件ID不能为空";
            }

            if (pluginSecret == null || pluginSecret.trim().isEmpty()) {
                return "插件密钥不能为空";
            }

            // 创建临时客户端验证凭证
            Client tempClient = Client.newBuilder(pluginId, pluginSecret)
                .openBaseUrl(getBaseUrl())
                .requestTimeout(10000) // 设置较短的超时时间用于测试
                .logReqAtDebug(false)
                .build();

            // 通过SDK的token管理器验证凭证有效性
            // 这里不进行实际的API调用，只验证能否成功创建客户端和获取token管理器
            if (tempClient != null) {
                logger.info("插件访问凭证验证成功");
                return null;
            } else {
                return "无法创建SDK客户端，请检查插件凭证";
            }

        } catch (Exception e) {
            String errorMsg = "插件访问凭证验证失败: " + e.getMessage();
            logger.warn(errorMsg);
            return errorMsg;
        }
    }

    /**
     * 测试手动访问凭证模式的token有效性
     */
    private String testManualAccessToken() {
        try {
            String accessToken = connection.getAccessToken();

            if (accessToken == null || accessToken.trim().isEmpty()) {
                return "访问令牌不能为空";
            }

            // 验证token格式（基本格式检查）
            if (!isValidTokenFormat(accessToken)) {
                return "访问令牌格式无效";
            }

            logger.info("手动访问凭证格式验证成功");
            return null;

        } catch (Exception e) {
            String errorMsg = "手动访问凭证验证失败: " + e.getMessage();
            logger.warn(errorMsg);
            return errorMsg;
        }
    }

    /**
     * 验证token格式是否有效
     * 进行基本的格式检查，不进行实际的API调用
     */
    private boolean isValidTokenFormat(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        // 基本长度检查（飞书token通常有一定的长度要求）
        String trimmedToken = token.trim();
        if (trimmedToken.length() < 10) {
            return false;
        }

        // 检查是否包含明显的无效字符
        if (trimmedToken.contains(" ") || trimmedToken.contains("\n") || trimmedToken.contains("\t")) {
            return false;
        }

        return true;
    }
    
    /**
     * 获取项目的工作项类型
     */
    public ListProjectWorkItemTypeResp getWorkItemTypes(@NotNull String projectKey) throws Exception {
        ListProjectWorkItemTypeReq req = ListProjectWorkItemTypeReq.newBuilder()
            .projectKey(projectKey)
            .build();

        return client.getProjectService().listProjectWorkItemType(req, defaultOptions);
    }

    /**
     * 获取SDK客户端（用于高级操作）
     */
    public Client getClient() {
        return client;
    }

    /**
     * 获取默认请求选项（用于高级操作）
     */
    public RequestOptions getDefaultOptions() {
        return defaultOptions;
    }
}
